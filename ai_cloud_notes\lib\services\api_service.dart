import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';

/// API服务类
/// 负责处理与后端API的通信
class ApiService {
  // 后端API地址
  static const String baseUrl = 'http://localhost:8080/api';

  // HTTP请求头
  Map<String, String> _headers = {
    'Content-Type': 'application/json',
  };

  // Dio实例
  late Dio _dio;

  // 单例模式
  static final ApiService _instance = ApiService._internal();

  factory ApiService() {
    return _instance;
  }

  ApiService._internal() {
    _initDio();
  }

  /// 初始化Dio客户端
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 15), // 连接超时15秒
      receiveTimeout: const Duration(seconds: 30), // 接收超时30秒
      sendTimeout: const Duration(seconds: 30), // 发送超时30秒
      headers: _headers,
      contentType: 'application/json',
      responseType: ResponseType.json,
    ));

    // 添加拦截器处理超时
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) {
        if (error.type == DioExceptionType.connectionTimeout) {
          error = error.copyWith(message: '连接超时，请检查网络连接');
        } else if (error.type == DioExceptionType.receiveTimeout) {
          error = error.copyWith(message: '响应超时，服务器响应过慢');
        } else if (error.type == DioExceptionType.sendTimeout) {
          error = error.copyWith(message: '发送超时，请检查网络连接');
        }
        handler.next(error);
      },
    ));
  }

  /// 初始化方法，加载存储的令牌
  Future<void> init() async {
    await _loadToken();
    // 重新初始化Dio，确保使用更新后的headers
    _initDio();
  }

  /// 从本地存储加载JWT令牌
  Future<void> _loadToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('jwt_token');
    print('[DEBUG_TOKEN] ApiService._loadToken: token from prefs: $token');
    if (token != null) {
      _headers['Authorization'] = 'Bearer $token';
      print(
          '[DEBUG_TOKEN] ApiService._headers after _loadToken: ${_headers['Authorization']}');
    } else {
      print('[DEBUG_TOKEN] ApiService._loadToken: no token found in prefs.');
    }
  }

  /// 设置JWT令牌
  Future<void> setToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('jwt_token', token);
    _headers['Authorization'] = 'Bearer $token';
    print('[DEBUG_TOKEN] ApiService.setToken: token set to: $token');
    print(
        '[DEBUG_TOKEN] ApiService._headers after setToken: ${_headers['Authorization']}');

    // 更新Dio的headers
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// 清除JWT令牌
  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('jwt_token');
    _headers.remove('Authorization');

    // 更新Dio的headers
    _dio.options.headers.remove('Authorization');
  }

  /// 发送验证码
  ///
  /// [email] 用户邮箱
  Future<Map<String, dynamic>> sendVerificationCode(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/send-verification-code'),
      headers: _headers,
      body: jsonEncode({'email': email}),
    );

    return jsonDecode(response.body);
  }

  /// 用户注册
  ///
  /// [username] 用户名
  /// [email] 邮箱
  /// [password] 密码
  /// [verificationCode] 验证码
  Future<Map<String, dynamic>> register({
    required String username,
    required String email,
    required String password,
    required String verificationCode,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: _headers,
      body: jsonEncode({
        'username': username,
        'email': email,
        'password': password,
        'verificationCode': verificationCode,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 用户登录
  ///
  /// [usernameOrEmail] 用户名或邮箱
  /// [password] 密码
  Future<Map<String, dynamic>> login({
    required String usernameOrEmail,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: _headers,
      body: jsonEncode({
        'usernameOrEmail': usernameOrEmail,
        'password': password,
      }),
    );

    final responseData = jsonDecode(response.body);

    // 如果登录成功，保存令牌
    if (responseData['success'] == true && responseData['data'] != null) {
      await setToken(responseData['data']['token']);
    }

    return responseData;
  }

  /// 获取当前登录用户信息
  Future<Map<String, dynamic>> getCurrentUser() async {
    final response = await http.get(
      Uri.parse('$baseUrl/auth/me'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 忘记密码
  ///
  /// [email] 用户邮箱
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/forgot-password'),
      headers: _headers,
      body: jsonEncode({'email': email}),
    );

    return jsonDecode(response.body);
  }

  /// 重置密码
  ///
  /// [email] 用户邮箱
  /// [token] 重置令牌
  /// [newPassword] 新密码
  Future<Map<String, dynamic>> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/reset-password'),
      headers: _headers,
      body: jsonEncode({
        'email': email,
        'token': token,
        'newPassword': newPassword,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 修改密码
  ///
  /// [currentPassword] 当前密码
  /// [newPassword] 新密码
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/change-password'),
      headers: _headers,
      body: jsonEncode({
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 用户登出
  Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/logout'),
        headers: _headers,
      );

      final responseData = jsonDecode(response.body);

      // 无论服务器响应如何，清除本地令牌
      await clearToken();

      return responseData;
    } catch (e) {
      // 即使请求失败，也清除本地令牌
      await clearToken();
      return {
        'success': false,
        'error': {'message': '登出失败: $e'}
      };
    }
  }

  // ---------- 笔记管理相关API ----------

  /// 获取笔记列表
  ///
  /// [page] 页码，从1开始
  /// [limit] 每页条数
  /// [tag] 标签过滤
  /// [favorite] 是否只返回收藏的笔记
  /// [archived] 是否只返回归档的笔记
  /// [search] 搜索关键词
  Future<Map<String, dynamic>> getNotes({
    int page = 1,
    int limit = 20,
    String? tag,
    bool? favorite,
    bool? archived,
    String? search,
  }) async {
    // 构建查询参数
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (tag != null) queryParams['tag'] = tag;
    if (favorite != null) queryParams['favorite'] = favorite.toString();
    if (archived != null) queryParams['archived'] = archived.toString();
    if (search != null && search.isNotEmpty) queryParams['search'] = search;

    final uri =
        Uri.parse('$baseUrl/notes').replace(queryParameters: queryParams);

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取笔记详情
  ///
  /// [id] 笔记ID
  Future<Map<String, dynamic>> getNoteById(String id) async {
    final response = await http.get(
      Uri.parse('$baseUrl/notes/$id'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 创建笔记
  ///
  /// [title] 标题
  /// [content] 内容
  /// [contentType] 内容类型（如plain-text, rich-text, markdown）
  /// [tags] 标签ID数组
  Future<Map<String, dynamic>> createNote({
    required String title,
    required String content,
    String contentType = 'rich-text',
    List<String> tags = const [],
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/notes'),
      headers: _headers,
      body: jsonEncode({
        'title': title,
        'content': content,
        'contentType': contentType,
        'tags': tags,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 更新笔记
  ///
  /// [id] 笔记ID
  /// [title] 标题
  /// [content] 内容
  /// [contentType] 内容类型
  /// [tags] 标签
  Future<Map<String, dynamic>> updateNote({
    required String id,
    String? title,
    String? content,
    String? contentType,
    List<String>? tags,
    bool isCompletingInitialSave = false,
  }) async {
    try {
      // 构建请求体
      final Map<String, dynamic> requestData = {};
      if (title != null) requestData['title'] = title;
      if (content != null) requestData['content'] = content;
      if (contentType != null) requestData['contentType'] = contentType;
      if (tags != null) requestData['tags'] = tags;
      requestData['isCompletingInitialSave'] = isCompletingInitialSave;

      // 使用Dio发送请求
      final response = await _dio.put(
        '/notes/$id',
        data: requestData,
      );

      return response.data;
    } catch (e) {
      _handleError('更新笔记失败', e);
      // 将DioError转换为统一的错误格式
      if (e is DioException) {
        final response = e.response;
        if (response != null) {
          return {
            'success': false,
            'error': {
              'message': '更新笔记失败: ${response.statusMessage}',
              'statusCode': response.statusCode,
              'data': response.data,
            }
          };
        }
        return {
          'success': false,
          'error': {
            'message': '更新笔记失败: ${e.message}',
            'type': e.type.toString(),
          }
        };
      }
      return {
        'success': false,
        'error': {'message': '更新笔记失败: $e'}
      };
    }
  }

  /// 删除笔记
  ///
  /// [id] 笔记ID
  Future<Map<String, dynamic>> deleteNote(String id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/notes/$id'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 切换笔记收藏状态
  ///
  /// [id] 笔记ID
  Future<Map<String, dynamic>> toggleFavorite(String id) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/notes/$id/favorite'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 切换笔记归档状态
  ///
  /// [id] 笔记ID
  Future<Map<String, dynamic>> toggleArchive(String id) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/notes/$id/archive'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 搜索笔记
  ///
  /// [query] 搜索关键词
  /// [page] 页码
  /// [limit] 每页条数
  /// [searchIn] 搜索范围: 'all'(默认), 'title', 'content'
  /// [startDate] 开始日期过滤
  /// [endDate] 结束日期过滤
  /// [tags] 标签ID数组
  /// [favorite] 是否仅搜索收藏的笔记
  Future<Map<String, dynamic>> searchNotes({
    required String query,
    int page = 1,
    int limit = 20,
    String searchIn = 'all',
    DateTime? startDate,
    DateTime? endDate,
    List<String>? tags,
    bool? favorite,
  }) async {
    // 构建查询参数
    final Map<String, String> queryParams = {
      'q': query,
      'page': page.toString(),
      'limit': limit.toString(),
      'searchIn': searchIn,
    };

    // 添加可选参数
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String();
    }

    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String();
    }

    if (tags != null && tags.isNotEmpty) {
      queryParams['tags'] = tags.join(',');
    }

    if (favorite != null) {
      queryParams['favorite'] = favorite.toString();
    }

    // 调用高级搜索API
    final uri = Uri.parse('$baseUrl/search/advanced').replace(
      queryParameters: queryParams,
    );

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取搜索历史
  ///
  /// [limit] 获取历史记录的条数
  Future<Map<String, dynamic>> getSearchHistory({int limit = 10}) async {
    final uri = Uri.parse('$baseUrl/search/history').replace(
      queryParameters: {
        'limit': limit.toString(),
      },
    );

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 添加搜索历史
  ///
  /// [query] 搜索关键词
  Future<Map<String, dynamic>> addSearchHistory(String query) async {
    final response = await http.post(
      Uri.parse('$baseUrl/search/history'),
      headers: _headers,
      body: jsonEncode({'query': query}),
    );

    return jsonDecode(response.body);
  }

  /// 清除搜索历史
  Future<Map<String, dynamic>> clearSearchHistory() async {
    final response = await http.delete(
      Uri.parse('$baseUrl/search/history'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取热门搜索
  ///
  /// [limit] 获取热门搜索的条数
  Future<Map<String, dynamic>> getPopularSearches({int limit = 10}) async {
    final uri = Uri.parse('$baseUrl/search/popular').replace(
      queryParameters: {
        'limit': limit.toString(),
      },
    );

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取笔记统计信息
  Future<Map<String, dynamic>> getNoteStats() async {
    final response = await http.get(
      Uri.parse('$baseUrl/notes/stats'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 批量操作笔记
  ///
  /// [noteIds] 笔记ID列表
  /// [operation] 操作类型：delete, archive, unarchive, favorite, unfavorite
  Future<Map<String, dynamic>> batchOperation({
    required List<String> noteIds,
    required String operation,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/notes/batch'),
      headers: _headers,
      body: jsonEncode({
        'noteIds': noteIds,
        'operation': operation,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 创建笔记分享链接
  ///
  /// [id] 笔记ID
  /// [expireHours] 过期时间(小时)，可选
  /// [isPublic] 是否公开分享，可选
  /// [accessType] 访问类型，'readonly' 或 'editable'，可选
  /// [password] 访问密码，可选
  Future<Map<String, dynamic>> createShareLink({
    required String id,
    int? expireHours,
    bool? isPublic,
    String? accessType,
    String? password,
  }) async {
    // 构建请求体，只包含非空字段
    final Map<String, dynamic> body = {};
    if (expireHours != null) body['expireHours'] = expireHours;
    if (isPublic != null) body['isPublic'] = isPublic;
    if (accessType != null) body['accessType'] = accessType;
    if (password != null && password.trim().isNotEmpty)
      body['password'] = password.trim();

    final response = await http.post(
      Uri.parse('$baseUrl/notes/$id/share'),
      headers: _headers,
      body: jsonEncode(body),
    );

    return jsonDecode(response.body);
  }

  /// 取消笔记分享
  ///
  /// [id] 笔记ID
  Future<Map<String, dynamic>> cancelShare(String id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/notes/$id/share'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取分享的笔记
  ///
  /// [token] 分享令牌
  /// [password] 访问密码，可选
  Future<Map<String, dynamic>> getSharedNote(String token,
      {String? password}) async {
    try {
      // 构建查询参数
      final Map<String, String> queryParams = {};
      if (password != null && password.trim().isNotEmpty) {
        queryParams['password'] = password.trim();
      }

      final uri = Uri.parse('$baseUrl/notes/shared/$token')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: _headers,
      );

      final result = jsonDecode(response.body);
      return result;
    } catch (e) {
      _handleError('获取分享笔记失败', e);
      return {
        'success': false,
        'error': {'message': '获取分享笔记失败: $e'}
      };
    }
  }

  /// 更新分享的笔记
  ///
  /// [token] 分享令牌
  /// [title] 标题（可选）
  /// [content] 内容（可选）
  /// [password] 访问密码（如果笔记受密码保护）
  Future<Map<String, dynamic>> updateSharedNote({
    required String token,
    String? title,
    String? content,
    String? password,
  }) async {
    try {
      // 构建请求体
      final Map<String, dynamic> requestData = {};
      if (title != null) requestData['title'] = title;
      if (content != null) requestData['content'] = content;
      if (password != null && password.trim().isNotEmpty) {
        requestData['password'] = password.trim();
      }

      // 构建查询参数（如果有密码）
      final Map<String, String> queryParams = {};
      if (password != null && password.trim().isNotEmpty) {
        queryParams['password'] = password.trim();
      }

      // 使用Dio发送请求
      final response = await _dio.put(
        '/notes/shared/$token',
        data: requestData,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      return response.data;
    } catch (e) {
      _handleError('更新分享笔记失败', e);
      // 将DioError转换为统一的错误格式
      if (e is DioException) {
        final response = e.response;
        if (response != null) {
          return {
            'success': false,
            'error': {
              'message': '更新分享笔记失败: ${response.statusMessage}',
              'statusCode': response.statusCode,
              'data': response.data,
            }
          };
        }
        return {
          'success': false,
          'error': {
            'message': '更新分享笔记失败: ${e.message}',
            'type': e.type.toString(),
          }
        };
      }
      return {
        'success': false,
        'error': {'message': '更新分享笔记失败: $e'}
      };
    }
  }

  // ---------- 标签管理相关API ----------

  /// 获取所有标签
  Future<Map<String, dynamic>> getTags() async {
    final response = await http.get(
      Uri.parse('$baseUrl/tags'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 创建标签
  ///
  /// [name] 标签名称
  /// [color] 标签颜色，十六进制格式 (如 #FF5733)
  Future<Map<String, dynamic>> createTag({
    required String name,
    String? color,
  }) async {
    print('DEBUG: [ApiService] 创建标签: $name, 颜色: $color');
    final Map<String, dynamic> body = {
      'name': name,
    };

    if (color != null) body['color'] = color;

    print('DEBUG: [ApiService] 请求体: ${jsonEncode(body)}');
    print('DEBUG: [ApiService] 请求URL: $baseUrl/tags');
    print('DEBUG: [ApiService] 请求头: $_headers');

    final response = await http.post(
      Uri.parse('$baseUrl/tags'),
      headers: _headers,
      body: jsonEncode(body),
    );

    final result = jsonDecode(response.body);
    print('DEBUG: [ApiService] 创建标签响应: $result');
    return result;
  }

  /// 获取标签详情
  ///
  /// [id] 标签ID
  Future<Map<String, dynamic>> getTagById(String id) async {
    final response = await http.get(
      Uri.parse('$baseUrl/tags/$id'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 更新标签
  ///
  /// [id] 标签ID
  /// [name] 新标签名称
  /// [color] 新标签颜色，十六进制格式
  Future<Map<String, dynamic>> updateTag({
    required String id,
    String? name,
    String? color,
  }) async {
    final Map<String, dynamic> body = {};
    if (name != null) body['name'] = name;
    if (color != null) body['color'] = color;

    final response = await http.put(
      Uri.parse('$baseUrl/tags/$id'),
      headers: _headers,
      body: jsonEncode(body),
    );

    return jsonDecode(response.body);
  }

  /// 删除标签
  ///
  /// [id] 标签ID
  Future<Map<String, dynamic>> deleteTag(String id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/tags/$id'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取标签关联的笔记
  ///
  /// [id] 标签ID
  /// [page] 页码
  /// [limit] 每页条数
  /// [sortBy] 排序字段
  /// [order] 排序方向 (asc 或 desc)
  Future<Map<String, dynamic>> getNotesByTag({
    required String id,
    int page = 1,
    int limit = 20,
    String sortBy = 'updatedAt',
    String order = 'desc',
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
      'sortBy': sortBy,
      'order': order,
    };

    final uri = Uri.parse('$baseUrl/tags/$id/notes')
        .replace(queryParameters: queryParams);

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取热门标签
  ///
  /// [limit] 返回标签数量
  Future<Map<String, dynamic>> getPopularTags({int limit = 10}) async {
    final queryParams = <String, String>{
      'limit': limit.toString(),
    };

    final uri = Uri.parse('$baseUrl/tags/popular')
        .replace(queryParameters: queryParams);

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取用户资料
  Future<Map<String, dynamic>> getUserProfile() async {
    final response = await http.get(
      Uri.parse('$baseUrl/users/profile'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 更新用户资料
  ///
  /// [username] 用户名
  /// [email] 邮箱
  /// [bio] 个人签名
  Future<Map<String, dynamic>> updateUserProfile({
    String? username,
    String? email,
    String? bio,
  }) async {
    final Map<String, dynamic> body = {};
    if (username != null) body['username'] = username;
    if (email != null) body['email'] = email;
    if (bio != null) body['bio'] = bio;

    final response = await http.put(
      Uri.parse('$baseUrl/users/profile'),
      headers: _headers,
      body: jsonEncode(body),
    );

    return jsonDecode(response.body);
  }

  /// 上传用户头像
  ///
  /// [file] 头像文件
  Future<Map<String, dynamic>> uploadAvatar(dynamic file) async {
    try {
      if (kIsWeb) {
        // Web平台处理逻辑 - 使用FormData直接发送
        final uri = Uri.parse('$baseUrl/users/avatar');

        // 创建multipart请求
        final request = http.MultipartRequest('POST', uri);

        // 添加认证头
        request.headers.addAll({
          'Authorization': _headers['Authorization'] ?? '',
        });

        // 添加文件
        if (file is Uint8List) {
          // 检测文件类型并设置正确的MIME类型
          String mimeType = 'image/png'; // 默认为PNG

          if (file.length > 2) {
            // 尝试通过魔数检测文件类型
            if (file[0] == 0x89 &&
                file[1] == 0x50 &&
                file[2] == 0x4E &&
                file[3] == 0x47) {
              mimeType = 'image/png';
            } else if (file[0] == 0xFF && file[1] == 0xD8) {
              mimeType = 'image/jpeg';
            } else if (file[0] == 0x47 && file[1] == 0x49 && file[2] == 0x46) {
              mimeType = 'image/gif';
            }
          }

          // 添加内存中的数据，指定正确的MIME类型
          request.files.add(
            http.MultipartFile.fromBytes(
              'avatar',
              file,
              filename: 'web_avatar.png',
              contentType: MediaType.parse(mimeType),
            ),
          );
        } else {
          return {
            'success': false,
            'error': {'message': '无效的图片数据'}
          };
        }

        // 发送请求
        final streamedResponse = await request.send();
        final response = await http.Response.fromStream(streamedResponse);

        // 添加详细的错误处理
        if (response.statusCode >= 400) {
          return {
            'success': false,
            'error': {
              'message': '上传失败: ${response.body}',
              'statusCode': response.statusCode
            }
          };
        }

        return jsonDecode(response.body);
      } else {
        // 移动平台处理逻辑
        var request = http.MultipartRequest(
          'POST',
          Uri.parse('$baseUrl/users/avatar'),
        );

        // 添加认证头
        request.headers.addAll({
          'Authorization': _headers['Authorization'] ?? '',
        });

        // 添加文件
        request.files.add(await http.MultipartFile.fromPath(
          'avatar',
          file.path,
        ));

        final streamedResponse = await request.send();
        final response = await http.Response.fromStream(streamedResponse);

        // 添加详细的错误处理
        if (response.statusCode >= 400) {
          return {
            'success': false,
            'error': {
              'message': '上传失败: ${response.body}',
              'statusCode': response.statusCode
            }
          };
        }

        return jsonDecode(response.body);
      }
    } catch (e) {
      return {
        'success': false,
        'error': {'message': '上传失败: $e'}
      };
    }
  }

  /// 获取用户统计信息
  Future<Map<String, dynamic>> getUserStats() async {
    final response = await http.get(
      Uri.parse('$baseUrl/users/stats'),
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 获取上次同步后的变更
  ///
  /// [lastSyncTime] 上次同步时间
  Future<Map<String, dynamic>> getChanges({
    DateTime? lastSyncTime,
  }) async {
    // 构建查询参数
    final queryParams = <String, String>{};

    if (lastSyncTime != null) {
      queryParams['lastSyncTime'] = lastSyncTime.toIso8601String();
    }

    final uri =
        Uri.parse('$baseUrl/notes/sync').replace(queryParameters: queryParams);

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 上传本地变更
  ///
  /// [notes] 需要同步的笔记列表
  /// [deletedNoteIds] 需要删除的笔记ID列表
  Future<Map<String, dynamic>> pushChanges({
    required List<Map<String, dynamic>> notes,
    List<String> deletedNoteIds = const [],
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/notes/sync'),
      headers: _headers,
      body: jsonEncode({
        'notes': notes,
        'deletedNoteIds': deletedNoteIds,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 导入笔记
  ///
  /// [notes] 笔记数据列表
  Future<Map<String, dynamic>> importNotes({
    required List<Map<String, dynamic>> notes,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/notes/import'),
      headers: _headers,
      body: jsonEncode({
        'notes': notes,
      }),
    );

    return jsonDecode(response.body);
  }

  /// 导出笔记
  ///
  /// [filter] 过滤条件，可选值: 'all', 'favorite', 'archived', 'active'
  Future<Map<String, dynamic>> exportNotes({
    String filter = 'all',
  }) async {
    final queryParams = <String, String>{};

    if (filter != 'all') {
      queryParams['filter'] = filter;
    }

    final uri = Uri.parse('$baseUrl/notes/export')
        .replace(queryParameters: queryParams);

    final response = await http.get(
      uri,
      headers: _headers,
    );

    return jsonDecode(response.body);
  }

  /// 上传笔记图片
  ///
  /// [file] 图片文件，可以是File(移动平台)或Uint8List(Web平台)
  /// [noteId] 可选的笔记ID
  /// [shareToken] 可选的分享令牌（用于分享页面上传图片）
  Future<Map<String, dynamic>> uploadNoteImage(dynamic file,
      {String? noteId, String? shareToken}) async {
    print(
        '[DEBUG_TOKEN] ApiService.uploadNoteImage: Current _headers[Authorization]: ${_headers['Authorization']}');
    try {
      FormData formData;

      if (kIsWeb) {
        // Web平台处理逻辑
        if (file is Uint8List) {
          // 检测图片类型
          String mimeType = 'image/png'; // 默认为PNG
          String fileName = 'image.png';

          if (file.length > 2) {
            // 尝试通过魔数检测文件类型
            if (file[0] == 0x89 &&
                file[1] == 0x50 &&
                file[2] == 0x4E &&
                file[3] == 0x47) {
              mimeType = 'image/png';
              fileName = 'image.png';
            } else if (file[0] == 0xFF && file[1] == 0xD8) {
              mimeType = 'image/jpeg';
              fileName = 'image.jpg';
            } else if (file[0] == 0x47 && file[1] == 0x49 && file[2] == 0x46) {
              mimeType = 'image/gif';
              fileName = 'image.gif';
            }
          }

          // 创建FormData，确保使用和移动平台相同的字段名'image'
          formData = FormData.fromMap({
            'image': MultipartFile.fromBytes(
              file,
              filename: fileName,
              contentType: MediaType.parse(mimeType),
            ),
          });
        } else {
          return {
            'success': false,
            'error': {'message': '无效的图片数据'}
          };
        }
      } else {
        // 移动平台处理逻辑
        formData = FormData.fromMap({
          'image': await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        });
      }

      // 构建请求URL，支持 noteId 和 shareToken 多参数拼接
      String url = '/notes/upload-image';
      final Map<String, String> queryParams = {};
      if (noteId != null && noteId.isNotEmpty) queryParams['noteId'] = noteId;
      if (shareToken != null && shareToken.isNotEmpty)
        queryParams['shareToken'] = shareToken;
      if (queryParams.isNotEmpty) {
        final uri = Uri.parse(url).replace(queryParameters: queryParams);
        url = uri.toString();
      }

      // 发送请求到图片上传接口
      final response = await _dio.post(
        url, // 使用可能包含noteId查询参数的URL
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          // 设置更长的超时时间
          sendTimeout: const Duration(minutes: 2),
          receiveTimeout: const Duration(minutes: 2),
        ),
      );

      return response.data;
    } catch (e) {
      _handleError('上传失败', e);
      // 转换DioError
      if (e is DioException) {
        final response = e.response;
        if (response != null) {
          return {
            'success': false,
            'error': {
              'message': '上传失败: ${response.statusMessage}',
              'statusCode': response.statusCode,
              'data': response.data,
            }
          };
        }
        return {
          'success': false,
          'error': {
            'message': '上传失败: ${e.message}',
            'type': e.type.toString(),
          }
        };
      }
      return {
        'success': false,
        'error': {'message': '上传失败: $e'}
      };
    }
  }

  /// 获取指定笔记的历史版本列表
  ///
  /// @param noteId 笔记的唯一标识符
  /// @return 包含历史版本数据的Map列表，每个Map代表一个历史版本
  /// @throws Exception 如果请求失败或服务器返回错误
  Future<List<Map<String, dynamic>>> getNoteHistory(String noteId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notes/$noteId/history'),
        headers: _headers,
      );

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        // 从响应数据中正确提取嵌套在data.history中的历史记录列表
        final historyList = List<Map<String, dynamic>>.from(
            responseData['data']['history'] ?? []);
        return historyList;
      } else {
        throw Exception(responseData['error']?['message'] ?? '获取历史记录失败');
      }
    } catch (e) {
      _handleError('获取历史记录失败', e);
      throw Exception('获取历史记录时发生错误: $e');
    }
  }

  /// 请求恢复笔记到指定的历史版本
  ///
  /// @param noteId 要恢复的笔记ID
  /// @param version 要恢复到的历史版本号
  /// @return 更新后的笔记数据 Map
  /// @throws Exception 如果请求失败或服务器返回错误
  Future<Map<String, dynamic>> restoreNoteVersion(
      String noteId, int version) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/notes/$noteId/restore/$version'),
        headers: _headers,
      );

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        // 正确从data.note中提取更新后的笔记数据
        final updatedNoteData =
            Map<String, dynamic>.from(responseData['data']['note'] ?? {});
        return updatedNoteData;
      } else {
        throw Exception(responseData['error']?['message'] ?? '恢复历史版本失败');
      }
    } catch (e) {
      _handleError('恢复历史版本失败', e);
      throw Exception('恢复历史版本时发生错误: $e');
    }
  }

  /// 获取用户主题设置
  Future<Map<String, dynamic>?> getUserThemeSettings() async {
    try {
      final response = await _dio.get(
        '/theme-settings',
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data['data'];
    } catch (e) {
      _handleError('获取主题设置失败', e);
      return null;
    }
  }

  /// 更新用户主题设置
  Future<bool> updateUserThemeSettings({
    required int themeMode,
    required String fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    try {
      final response = await _dio.post(
        '/theme-settings',
        data: {
          'themeMode': themeMode,
          'fontColor': fontColor,
          'textSize': textSize,
          'titleSize': titleSize,
        },
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data['success'] == true;
    } catch (e) {
      _handleError('更新主题设置失败', e);
      return false;
    }
  }

  /// 获取用户AI设置
  Future<Map<String, dynamic>?> getUserAISettings() async {
    try {
      final response = await _dio.get(
        '/ai-settings',
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data['data'];
    } catch (e) {
      _handleError('获取AI设置失败', e);
      return null;
    }
  }

  /// 更新用户AI设置
  Future<bool> updateUserAISettings({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    try {
      final response = await _dio.post(
        '/ai-settings',
        data: {
          'aiAssistantEnabled': aiAssistantEnabled,
          'smartSuggestionsEnabled': smartSuggestionsEnabled,
          'autoTaggingEnabled': autoTaggingEnabled,
          'contentSummaryEnabled': contentSummaryEnabled,
          'selectedModel': selectedModel,
          'suggestionFrequency': suggestionFrequency,
          'localProcessingEnabled': localProcessingEnabled,
          'allowDataCollection': allowDataCollection,
        },
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data['success'] == true;
    } catch (e) {
      _handleError('更新AI设置失败', e);
      return false;
    }
  }

  /// 获取认证请求头
  Future<Map<String, String>> _getAuthHeaders() async {
    // 从本地存储获取token
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('jwt_token');

    // 构建认证请求头
    final headers = <String, String>{
      'Content-Type': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // ---------- AI功能相关API ----------

  /// 生成智能预测
  ///
  /// [content] 当前内容
  /// [cursorPosition] 光标位置
  /// [options] 选项
  Future<Map<String, dynamic>> generateCompletion({
    required String content,
    int? cursorPosition,
    Map<String, dynamic>? options,
  }) async {
    try {
      // 如果没有提供光标位置，默认使用内容长度
      final position = cursorPosition ?? content.length;

      // 将光标位置添加到选项中
      final Map<String, dynamic> updatedOptions = {
        ...options ?? {},
        'cursorPosition': position,
      };

      final response = await _dio.post(
        '/ai/completion',
        data: {
          'content': content,
          'options': updatedOptions,
        },
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data;
    } catch (e) {
      _handleError('生成智能预测失败', e);
      return {
        'success': false,
        'error': {'message': '生成智能预测失败: $e'}
      };
    }
  }

  /// 生成内容摘要
  ///
  /// [content] 笔记内容
  /// [options] 选项
  Future<Map<String, dynamic>> generateSummary({
    required String content,
    Map<String, dynamic>? options,
  }) async {
    try {
      final response = await _dio.post(
        '/ai/summary',
        data: {
          'content': content,
          'options': options,
        },
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data;
    } catch (e) {
      _handleError('生成内容摘要失败', e);
      return {
        'success': false,
        'error': {'message': '生成内容摘要失败: $e'}
      };
    }
  }

  /// 生成标签建议
  ///
  /// [content] 笔记内容
  /// [options] 选项
  Future<Map<String, dynamic>> generateTagSuggestions({
    required String content,
    Map<String, dynamic>? options,
  }) async {
    try {
      final response = await _dio.post(
        '/ai/tags',
        data: {
          'content': content,
          'options': options,
        },
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data;
    } catch (e) {
      _handleError('生成标签建议失败', e);
      return {
        'success': false,
        'error': {'message': '生成标签建议失败: $e'}
      };
    }
  }

  /// 智能问答
  ///
  /// [question] 问题
  /// [context] 上下文（可选）
  /// [options] 选项
  Future<Map<String, dynamic>> askQuestion({
    required String question,
    String? context,
    Map<String, dynamic>? options,
  }) async {
    try {
      final data = <String, dynamic>{
        'question': question,
        'options': options,
      };

      if (context != null) {
        data['context'] = context;
      }

      final response = await _dio.post(
        '/ai/ask',
        data: data,
        options: Options(
          headers: await _getAuthHeaders(),
        ),
      );

      return response.data;
    } catch (e) {
      _handleError('智能问答失败', e);
      return {
        'success': false,
        'error': {'message': '智能问答失败: $e'}
      };
    }
  }

  /// 处理API错误
  void _handleError(String message, dynamic error) {
    // 打印错误信息
    print('API错误: $message');
    print('详细信息: $error');

    // 如果是DioError，输出更详细的信息
    if (error is DioException) {
      final response = error.response;
      if (response != null) {
        print('状态码: ${response.statusCode}');
        print('状态信息: ${response.statusMessage}');
        print('响应数据: ${response.data}');
      }
      print('错误类型: ${error.type}');
      print('错误信息: ${error.message}');
    }
  }
}
