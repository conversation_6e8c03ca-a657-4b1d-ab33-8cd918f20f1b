import Redis from 'ioredis';
import { config } from '../config';
import { logger } from './logger';

/**
 * Redis客户端实例
 */
const redisClient = new Redis(config.redis.uri);

/**
 * 生成带前缀的键名
 * @param key 键名
 * @returns 带前缀的键名
 */
const prefixKey = (key: string): string => {
  return `${config.redis.prefix}${key}`;
};

/**
 * 设置键值对
 * @param key 键名
 * @param value 值
 * @param expire 过期时间（秒）
 */
export const setCache = async (key: string, value: string, expire = config.redis.expire): Promise<boolean> => {
  try {
    const prefixedKey = prefixKey(key);
    await redisClient.set(prefixedKey, value);
    await redisClient.expire(prefixedKey, expire);
    return true;
  } catch (error: any) {
    logger.error(`Redis设置缓存失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 获取键值
 * @param key 键名
 * @returns 值或null
 */
export const getCache = async (key: string): Promise<string | null> => {
  try {
    const prefixedKey = prefixKey(key);
    return await redisClient.get(prefixedKey);
  } catch (error: any) {
    logger.error(`Redis获取缓存失败: ${error.message}`, { stack: error.stack });
    return null;
  }
};

/**
 * 删除键
 * @param key 键名
 */
export const deleteCache = async (key: string): Promise<boolean> => {
  try {
    const prefixedKey = prefixKey(key);
    await redisClient.del(prefixedKey);
    return true;
  } catch (error: any) {
    logger.error(`Redis删除缓存失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 生成随机验证码
 * @param length 验证码长度
 * @returns 验证码
 */
export const generateVerificationCode = (length: number = 6): string => {
  const chars = '0123456789';
  let code = '';
  for (let i = 0; i < length; i++) {
    code += chars[Math.floor(Math.random() * chars.length)];
  }
  return code;
};

/**
 * 设置邮箱验证码
 * @param email 邮箱
 * @param code 验证码
 * @param expire 过期时间（秒）
 */
export const setEmailVerificationCode = async (email: string, code: string, expire = config.redis.expire): Promise<boolean> => {
  return await setCache(`verification:email:${email}`, code, expire);
};

/**
 * 获取邮箱验证码
 * @param email 邮箱
 */
export const getEmailVerificationCode = async (email: string): Promise<string | null> => {
  return await getCache(`verification:email:${email}`);
};

/**
 * 验证邮箱验证码
 * @param email 邮箱
 * @param code 验证码
 */
export const verifyEmailCode = async (email: string, code: string): Promise<boolean> => {
  const storedCode = await getEmailVerificationCode(email);
  if (!storedCode) {
    return false;
  }
  
  // 验证码匹配检查（忽略大小写）
  const isValid = storedCode.toLowerCase() === code.toLowerCase();
  
  // 如果验证成功，删除验证码（一次性使用）
  if (isValid) {
    await deleteCache(`verification:email:${email}`);
  }
  
  return isValid;
};

/**
 * 将JWT令牌加入黑名单
 * @param token JWT令牌
 * @param expire 过期时间(秒)，应与令牌剩余有效期一致
 */
export const addTokenToBlacklist = async (token: string, expire: number): Promise<boolean> => {
  try {
    return await setCache(`blacklist:token:${token}`, '1', expire);
  } catch (error: any) {
    logger.error(`将令牌加入黑名单失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 检查令牌是否在黑名单中
 * @param token JWT令牌
 */
export const isTokenBlacklisted = async (token: string): Promise<boolean> => {
  try {
    const result = await getCache(`blacklist:token:${token}`);
    return result !== null;
  } catch (error: any) {
    logger.error(`检查令牌黑名单失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 存储用户会话信息
 * @param userId 用户ID
 * @param sessionData 会话数据
 * @param expire 过期时间(秒)
 */
export const setUserSession = async (userId: string, sessionData: object, expire = 60 * 60 * 24 * 7): Promise<boolean> => {
  try {
    return await setCache(`session:user:${userId}`, JSON.stringify(sessionData), expire);
  } catch (error: any) {
    logger.error(`存储用户会话失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 获取用户会话信息
 * @param userId 用户ID
 */
export const getUserSession = async (userId: string): Promise<object | null> => {
  try {
    const data = await getCache(`session:user:${userId}`);
    return data ? JSON.parse(data) : null;
  } catch (error: any) {
    logger.error(`获取用户会话失败: ${error.message}`, { stack: error.stack });
    return null;
  }
};

// 检查Redis连接状态
redisClient.on('connect', () => {
  logger.info('Redis连接成功');
});

redisClient.on('error', (error) => {
  logger.error(`Redis连接错误: ${error.message}`);
});

export default redisClient; 