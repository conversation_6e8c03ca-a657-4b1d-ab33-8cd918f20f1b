import mongoose from 'mongoose';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 数据库连接URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai_cloud_notes';

/**
 * 连接数据库
 */
export const connectDB = async (): Promise<void> => {
  try {
    const conn = await mongoose.connect(MONGODB_URI);
    console.log(`MongoDB连接成功: ${conn.connection.host}`);
  } catch (error) {
    console.error(`MongoDB连接失败: ${error}`);
    process.exit(1);
  }
};

/**
 * 关闭数据库连接
 */
export const closeDB = async (): Promise<void> => {
  try {
    await mongoose.connection.close();
    console.log('MongoDB连接已关闭');
  } catch (error) {
    console.error(`关闭MongoDB连接失败: ${error}`);
  }
}; 